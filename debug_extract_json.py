#!/usr/bin/env python3
"""
调试extract_json_from_response函数
"""

import json
import sys
sys.path.append('src')

from utils.llm import extract_json_from_response

# 测试第一个问题
test_json = '''```json
{
  "decisions": {
    "AAPL": {
      "action": "sell",
      "quantity": 0, // No long position to sell
      "confidence": 60.0,
      "reasoning": "Despite the mixed signals from various analysts..."
    }
  }
}
```'''

print("测试extract_json_from_response:")
print("原始JSON:")
print(test_json)
print()

result = extract_json_from_response(test_json)
if result:
    print("✅ 成功:", result)
else:
    print("❌ 失败")
