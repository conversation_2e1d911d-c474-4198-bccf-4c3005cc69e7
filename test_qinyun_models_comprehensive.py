#!/usr/bin/env python3
"""
综合测试qinyun API所有模型的可用性
"""

import os
import sys
import json
import time
from datetime import datetime
from pydantic import BaseModel

# 添加src目录到路径
sys.path.append('src')

from utils.llm import call_llm

class TestResponse(BaseModel):
    """简单的测试响应模型"""
    answer: str
    status: str

def test_qinyun_model(model_name, display_name):
    """测试单个qinyun模型"""
    print(f"\n{'='*60}")
    print(f"测试模型: {display_name}")
    print(f"模型名称: {model_name}")
    print(f"{'='*60}")
    
    # 简单的测试提示
    test_prompt = """请用JSON格式回答以下问题：
{
  "answer": "你好，我是AI助手",
  "status": "working"
}

请确保返回有效的JSON格式。"""
    
    try:
        start_time = time.time()
        response = call_llm(
            prompt=test_prompt,
            model_name=model_name,
            model_provider="QingYun",
            pydantic_model=TestResponse
        )
        end_time = time.time()
        
        print(f"✅ 成功 - 响应时间: {end_time - start_time:.2f}秒")
        response_str = str(response) if hasattr(response, '__dict__') else str(response)
        print(f"响应内容: {response_str[:200]}...")

        return {
            "model_name": model_name,
            "display_name": display_name,
            "status": "success",
            "response_time": end_time - start_time,
            "response_preview": response_str[:200]
        }
        
    except Exception as e:
        print(f"❌ 失败 - 错误: {str(e)}")
        return {
            "model_name": model_name,
            "display_name": display_name,
            "status": "failed",
            "error": str(e)
        }

def main():
    """主测试函数"""
    print("开始测试qinyun API所有模型...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 从api_models.json读取qinyun模型
    with open('src/llm/api_models.json', 'r', encoding='utf-8') as f:
        models = json.load(f)
    
    # 筛选qinyun模型（排除custom）
    qinyun_models = [
        model for model in models 
        if model['provider'] == 'QingYun' and model['model_name'] != '-'
    ]
    
    print(f"找到 {len(qinyun_models)} 个qinyun模型进行测试")
    
    results = []
    successful_models = []
    failed_models = []
    
    for i, model in enumerate(qinyun_models, 1):
        print(f"\n进度: {i}/{len(qinyun_models)}")
        
        result = test_qinyun_model(model['model_name'], model['display_name'])
        results.append(result)
        
        if result['status'] == 'success':
            successful_models.append(result)
        else:
            failed_models.append(result)
        
        # 避免API限制，每次测试后等待
        if i < len(qinyun_models):
            print("等待3秒...")
            time.sleep(3)
    
    # 生成测试报告
    print(f"\n{'='*80}")
    print("测试报告")
    print(f"{'='*80}")
    print(f"总模型数: {len(qinyun_models)}")
    print(f"成功模型数: {len(successful_models)}")
    print(f"失败模型数: {len(failed_models)}")
    print(f"成功率: {len(successful_models)/len(qinyun_models)*100:.1f}%")
    
    print(f"\n✅ 可用模型 ({len(successful_models)}个):")
    for model in successful_models:
        print(f"  - {model['display_name']} (响应时间: {model['response_time']:.2f}s)")
    
    print(f"\n❌ 不可用模型 ({len(failed_models)}个):")
    for model in failed_models:
        print(f"  - {model['display_name']}: {model['error']}")
    
    # 保存详细结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'qinyun_models_test_report_{timestamp}.json'
    
    report_data = {
        'test_time': datetime.now().isoformat(),
        'total_models': len(qinyun_models),
        'successful_count': len(successful_models),
        'failed_count': len(failed_models),
        'success_rate': len(successful_models)/len(qinyun_models)*100,
        'successful_models': successful_models,
        'failed_models': failed_models,
        'all_results': results
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细测试报告已保存到: {report_file}")
    
    return successful_models, failed_models

if __name__ == "__main__":
    successful, failed = main()
