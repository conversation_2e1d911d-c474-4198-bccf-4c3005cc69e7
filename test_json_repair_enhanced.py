#!/usr/bin/env python3
"""
测试增强的JSON修复功能
验证能否处理glm-4-flash模型的JSON格式问题
"""

import os
import sys
import json
from datetime import datetime

# 添加src目录到路径
sys.path.append('src')

from utils.llm import attempt_json_repair, extract_json_from_response

def test_json_repair():
    """测试JSON修复功能"""
    print("开始测试增强的JSON修复功能...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 测试用例：基于实际错误日志的问题
    test_cases = [
        {
            "name": "带注释的JSON (glm-4-flash问题1)",
            "json_text": '''```json
{
  "decisions": {
    "AAPL": {
      "action": "sell",
      "quantity": 0, // No long position to sell
      "confidence": 60.0,
      "reasoning": "Despite the mixed signals from various analysts..."
    }
  }
}
```''',
            "expected_keys": ["decisions"]
        },
        {
            "name": "缺少引号的JSON (glm-4-flash问题2)",
            "json_text": '''```json
{
  "signal": "bullish",
  "confidence 80,
  "reasoning": "Alright, let's talk about AAPL, folks..."
}
```''',
            "expected_keys": ["signal", "confidence", "reasoning"]
        },
        {
            "name": "混合格式问题",
            "json_text": '''```json
{
  "signal": "bearish",
  confidence: 75, // Missing quotes
  "reasoning": "Market concerns about..."
}
```''',
            "expected_keys": ["signal", "confidence", "reasoning"]
        },
        {
            "name": "缺少冒号的JSON",
            "json_text": '''```json
{
  "signal" "neutral",
  "confidence": 50,
  "reasoning": "Mixed signals from the market"
}
```''',
            "expected_keys": ["signal", "confidence", "reasoning"]
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n进度: {i}/{total_count}")
        print("="*60)
        print(f"测试: {test_case['name']}")
        print("="*60)
        
        print("原始JSON:")
        print(test_case['json_text'][:200] + "..." if len(test_case['json_text']) > 200 else test_case['json_text'])
        print()
        
        # 测试extract_json_from_response
        print("🔍 使用extract_json_from_response...")
        result1 = extract_json_from_response(test_case['json_text'])
        
        if result1:
            print(f"✅ extract_json_from_response成功: {result1}")
            # 检查是否包含期望的键
            missing_keys = [key for key in test_case['expected_keys'] if key not in result1]
            if not missing_keys:
                print(f"✅ 包含所有期望的键: {test_case['expected_keys']}")
                success_count += 1
            else:
                print(f"⚠️  缺少键: {missing_keys}")
        else:
            print("❌ extract_json_from_response失败")
            
            # 测试attempt_json_repair
            print("🔧 尝试attempt_json_repair...")
            # 提取JSON部分
            json_part = test_case['json_text']
            if '```json' in json_part:
                start = json_part.find('```json') + 7
                end = json_part.find('```', start)
                if end != -1:
                    json_part = json_part[start:end].strip()
                else:
                    json_part = json_part[start:].strip()
            
            result2 = attempt_json_repair(json_part)
            if result2:
                print(f"✅ attempt_json_repair成功: {result2}")
                missing_keys = [key for key in test_case['expected_keys'] if key not in result2]
                if not missing_keys:
                    print(f"✅ 包含所有期望的键: {test_case['expected_keys']}")
                    success_count += 1
                else:
                    print(f"⚠️  缺少键: {missing_keys}")
            else:
                print("❌ attempt_json_repair也失败")
        
        print("-" * 60)
    
    print(f"\n{'='*60}")
    print("测试报告")
    print(f"{'='*60}")
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试通过！JSON修复功能工作正常")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    test_json_repair()
