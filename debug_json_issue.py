#!/usr/bin/env python3
"""
调试JSON问题
"""

import json
import sys
sys.path.append('src')

from utils.llm import preprocess_malformed_json

# 测试第一个问题
test_json = '''```json
{
  "decisions": {
    "AAPL": {
      "action": "sell",
      "quantity": 0, // No long position to sell
      "confidence": 60.0,
      "reasoning": "Despite the mixed signals from various analysts..."
    }
  }
}
```'''

print("原始JSON:")
print(test_json)
print()

# 提取JSON部分
if '```json' in test_json:
    start = test_json.find('```json') + 7
    end = test_json.find('```', start)
    if end != -1:
        json_part = test_json[start:end].strip()
    else:
        json_part = test_json[start:].strip()

print("提取的JSON部分:")
print(repr(json_part))
print()

# 预处理
processed = preprocess_malformed_json(json_part)
print("预处理后:")
print(repr(processed))
print()

# 尝试解析
try:
    result = json.loads(processed)
    print("✅ 解析成功:", result)
except json.JSONDecodeError as e:
    print("❌ 解析失败:", e)
    print("错误位置:", processed[max(0, e.pos-10):e.pos+10])
    print("字符:", repr(processed[e.pos-1:e.pos+1]) if e.pos > 0 else "开头")
