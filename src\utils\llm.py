"""Helper functions for LLM"""

import json
import logging
import time
from typing import TypeVar, Type, Optional, Any
from pydantic import BaseModel
from src.llm.models import get_model, get_model_info, ModelProvider
from src.utils.progress import progress

# Import deterministic utilities
try:
    from src.utils.deterministic import (
        is_deterministic_mode,
        get_deterministic_seed,
        create_deterministic_hash,
        validate_deterministic_response
    )
except ImportError:
    # Fallback if deterministic module is not available
    def is_deterministic_mode(): return False
    def get_deterministic_seed(): return 42
    def create_deterministic_hash(data, context=""): return ""
    def validate_deterministic_response(response, keys): return True

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


def call_llm(
    prompt: Any,
    model_name: str,
    model_provider: str,
    pydantic_model: Type[T],
    agent_name: Optional[str] = None,
    max_retries: int = 3,
    default_factory=None,
) -> T:
    """
    Makes an LLM call with retry logic, handling both JSON supported and non-JSON supported models.
    Enhanced with deterministic behavior support and grok-beta prompt optimization.

    Args:
        prompt: The prompt to send to the LLM
        model_name: Name of the model to use
        model_provider: Provider of the model
        pydantic_model: The Pydantic model class to structure the output
        agent_name: Optional name of the agent for progress updates
        max_retries: Maximum number of retries (default: 3)
        default_factory: Optional factory function to create default response on failure

    Returns:
        An instance of the specified Pydantic model
    """

    model_info = get_model_info(model_name, model_provider)
    llm = get_model(model_name, ModelProvider(model_provider))

    # Log deterministic mode status
    if is_deterministic_mode():
        logger.debug(f"Deterministic LLM call for {agent_name or 'unknown'} with seed {get_deterministic_seed()}")
        # In deterministic mode, use fewer retries to avoid masking issues
        max_retries = min(max_retries, 2)

    # Only use structured output for models that support JSON mode
    if model_info and model_info.has_json_mode():
        llm = llm.with_structured_output(
            pydantic_model,
            method="json_mode",
        )

    # Store original prompt for grok-beta optimization
    original_prompt = prompt
    current_prompt = prompt

    # Call the LLM with retries
    for attempt in range(max_retries):
        try:
            # Call the LLM
            result = llm.invoke(current_prompt)

            # For non-JSON support models, we need to extract and parse the JSON manually
            if model_info and not model_info.has_json_mode():
                # Extract content from the result
                content = ""
                if hasattr(result, 'content'):
                    content = str(getattr(result, 'content', ''))
                else:
                    content = str(result)

                parsed_result = extract_json_from_response(content)
                if parsed_result:
                    try:
                        # Validate that the response is not truncated for critical fields
                        if is_response_truncated(content, parsed_result):
                            # Log truncation warning
                            if agent_name:
                                progress.update_status(agent_name, None, f"Warning: Response appears truncated, attempt {attempt + 1}")

                            # For grok-beta model, optimize prompt for next attempt
                            if model_provider == "QingYun" and "grok" in model_name.lower() and attempt < max_retries - 1:
                                current_prompt = optimize_prompt_for_grok(original_prompt, attempt + 1)
                                print(f"🔧 Optimizing prompt for grok-beta model (attempt {attempt + 2}/{max_retries})")
                                print(f"   Detected truncation in response: {content[:100]}...")
                                raise ValueError(f"Response truncated for grok-beta model, retrying with optimized prompt")
                            elif attempt >= max_retries - 1:
                                # 最后一次尝试失败，尝试使用部分解析的结果
                                print(f"⚠️  Final attempt failed for grok-beta, trying to use partial result...")
                                try:
                                    # 尝试修复不完整的JSON并创建模型
                                    repaired_result = attempt_json_repair(content)
                                    if repaired_result:
                                        print(f"✅ Successfully repaired JSON for {agent_name}")
                                        return pydantic_model(**repaired_result)
                                except Exception as repair_error:
                                    print(f"❌ JSON repair failed: {repair_error}")

                                # 如果修复失败，抛出原始错误
                                raise ValueError(f"Response truncated for grok-beta model, all repair attempts failed")

                        return pydantic_model(**parsed_result)
                    except Exception as e:
                        raise ValueError(f"Failed to create Pydantic model from parsed JSON: {e}")
                else:
                    # If JSON extraction failed, raise an exception to trigger retry
                    raise ValueError(f"Failed to extract valid JSON from response: {content[:200]}...")
            else:
                # For models with JSON mode, result should already be the correct type
                if isinstance(result, pydantic_model):
                    return result
                else:
                    # Try to convert if needed
                    try:
                        return pydantic_model(**result) if isinstance(result, dict) else result
                    except Exception:
                        return result

        except Exception as e:
            if agent_name:
                progress.update_status(agent_name, None, f"Error - retry {attempt + 1}/{max_retries}")

            error_str = str(e).lower()

            # 记录每次重试的错误详情
            retry_error_details = {
                "error_type": type(e).__name__,
                "error_message": str(e)[:500],  # 限制错误消息长度
                "model_name": model_name,
                "model_provider": model_provider,
                "agent_name": agent_name,
                "attempt_number": attempt + 1,
                "max_retries": max_retries,
                "timestamp": None
            }

            # 记录重试错误到日志文件
            if agent_name:
                try:
                    import os
                    from datetime import datetime

                    retry_error_details["timestamp"] = datetime.now().isoformat()

                    error_log_dir = "reasoning_logs/retry_errors"
                    os.makedirs(error_log_dir, exist_ok=True)

                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    retry_log_file = f"{error_log_dir}/{agent_name}_retry_{attempt + 1}_{timestamp}.json"

                    with open(retry_log_file, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(retry_error_details, f, indent=2, ensure_ascii=False)

                    print(f"Retry error details saved to: {retry_log_file}")
                except Exception as log_error:
                    print(f"Failed to save retry error log: {log_error}")

            print(f"Attempt {attempt + 1}/{max_retries} failed for {agent_name}: {type(e).__name__}: {str(e)[:200]}...")

            # Special handling for GROQ rate limit errors
            if model_provider.lower() == "groq" and ("429" in str(e) or "rate limit" in error_str):
                from src.utils.groq_api_manager import handle_groq_rate_limit, is_groq_rate_limit_error

                if is_groq_rate_limit_error(str(e)):
                    print(f"检测到GROQ速率限制错误: {str(e)[:100]}...")

                    # 尝试切换API密钥
                    if handle_groq_rate_limit(str(e)):
                        print("已切换到新的GROQ API密钥，重新创建LLM实例...")

                        # 重新创建LLM实例使用新的API密钥
                        try:
                            llm = get_model(model_name, ModelProvider(model_provider))

                            # 重新应用structured output配置
                            if model_info and model_info.has_json_mode():
                                llm = llm.with_structured_output(
                                    pydantic_model,
                                    method="json_mode",
                                )

                            print("LLM实例重新创建成功，继续重试...")
                            continue  # 继续重试而不等待

                        except Exception as recreate_error:
                            print(f"重新创建LLM实例失败: {recreate_error}")
                    else:
                        print("无法切换到新的API密钥，所有GROQ密钥可能都已达到限制")

            # Special handling for SSL/Connection errors (especially for GROQ)
            elif any(ssl_error in error_str for ssl_error in [
                "ssl", "unexpected_eof_while_reading", "eof occurred in violation of protocol",
                "connecterror", "connection", "timeout", "read timeout"
            ]):
                if attempt < max_retries - 1:
                    # 对于SSL/连接错误，使用指数退避策略
                    wait_time = min(2 ** attempt, 30)  # 2s, 4s, 8s, 16s, max 30s
                    print(f"检测到连接错误 (SSL/网络): {str(e)[:100]}...")
                    print(f"等待 {wait_time} 秒后重试 ({attempt + 2}/{max_retries})...")
                    time.sleep(wait_time)

                    # 对于GROQ的SSL错误，尝试切换API密钥
                    if model_provider.lower() == "groq":
                        from src.utils.groq_api_manager import handle_groq_rate_limit
                        print("尝试切换GROQ API密钥以解决连接问题...")
                        if handle_groq_rate_limit("Connection error - switching key"):
                            try:
                                llm = get_model(model_name, ModelProvider(model_provider))
                                if model_info and model_info.has_json_mode():
                                    llm = llm.with_structured_output(pydantic_model, method="json_mode")
                                print("已切换API密钥并重新创建LLM实例")
                            except Exception as recreate_error:
                                print(f"重新创建LLM实例失败: {recreate_error}")

                    continue

            # Special handling for QingYun API rate limit errors
            elif model_provider.lower() == "qingyun" and ("429" in str(e) or "rate limit" in error_str):
                if attempt < max_retries - 1:
                    # Enhanced exponential backoff for QingYun API
                    base_wait = 90  # Start with 90 seconds for QingYun
                    wait_time = min(base_wait * (2 ** attempt), 600)  # 90s, 180s, 360s, max 600s
                    print(f"QingYun API rate limit detected, waiting {wait_time} seconds before retry {attempt + 2}/{max_retries}...")
                    print(f"QingYun API server may be saturated, using extended backoff strategy...")
                    time.sleep(wait_time)
                    continue

            # General rate limit handling for other providers
            elif "429" in str(e) or "rate limit" in error_str:
                if attempt < max_retries - 1:
                    # Wait longer for rate limit errors
                    wait_time = min(60 * (attempt + 1), 300)  # 60s, 120s, 180s, max 300s
                    print(f"Rate limit hit, waiting {wait_time} seconds before retry {attempt + 2}/{max_retries}...")
                    time.sleep(wait_time)
                    continue

            if attempt == max_retries - 1:
                error_details = {
                    "error_type": type(e).__name__,
                    "error_message": str(e)[:500],  # 限制错误消息长度
                    "model_name": model_name,
                    "model_provider": model_provider,
                    "agent_name": agent_name,
                    "attempt_count": max_retries
                }

                print(f"Error in LLM call after {max_retries} attempts: {e}")
                print(f"Error details: {error_details}")

                # 记录详细错误到日志文件
                if agent_name:
                    try:
                        import os
                        from datetime import datetime

                        error_log_dir = "reasoning_logs/errors"
                        os.makedirs(error_log_dir, exist_ok=True)

                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        error_log_file = f"{error_log_dir}/{agent_name}_error_{timestamp}.json"

                        with open(error_log_file, 'w', encoding='utf-8') as f:
                            import json
                            json.dump(error_details, f, indent=2, ensure_ascii=False)

                        print(f"Error details saved to: {error_log_file}")
                    except Exception as log_error:
                        print(f"Failed to save error log: {log_error}")

                # Use default_factory if provided, otherwise create a basic default
                if default_factory:
                    return default_factory()
                return create_default_response(pydantic_model)

    # This should never be reached due to the retry logic above
    return create_default_response(pydantic_model)


def create_default_response(model_class: Type[T]) -> T:
    """Creates a safe default response based on the model's fields."""
    default_values = {}
    for field_name, field in model_class.model_fields.items():
        if field.annotation == str:
            default_values[field_name] = "Error in analysis, using default"
        elif field.annotation == float:
            default_values[field_name] = 0.0
        elif field.annotation == int:
            default_values[field_name] = 0
        elif hasattr(field.annotation, "__origin__") and getattr(field.annotation, "__origin__", None) == dict:
            default_values[field_name] = {}
        else:
            # For other types (like Literal), try to use the first allowed value
            if hasattr(field.annotation, "__args__"):
                args = getattr(field.annotation, "__args__", None)
                if args:
                    default_values[field_name] = args[0]
                else:
                    default_values[field_name] = None
            else:
                default_values[field_name] = None

    return model_class(**default_values)


def sanitize_json_string(content: str) -> str:
    """Sanitizes a string by removing or replacing invalid control characters that can break JSON parsing."""
    import re

    if not content:
        return content

    # Remove or replace common problematic control characters
    # Keep only printable characters, spaces, tabs, and newlines
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)

    # Replace problematic Unicode characters that might cause issues
    sanitized = sanitized.replace('\u2028', '\n')  # Line separator
    sanitized = sanitized.replace('\u2029', '\n')  # Paragraph separator

    # Fix common encoding issues
    sanitized = sanitized.replace('\ufffd', '')  # Replacement character

    # Remove any remaining non-printable characters that might cause JSON parsing issues
    # This is more aggressive cleaning for grok-beta model responses
    sanitized = re.sub(r'[\x80-\x9F]', '', sanitized)  # Remove C1 control characters

    # Fix common issues with quotes and escaping
    sanitized = re.sub(r'[\u201C\u201D]', '"', sanitized)  # Replace smart quotes with regular quotes
    sanitized = re.sub(r'[\u2018\u2019]', "'", sanitized)  # Replace smart apostrophes

    # Remove any remaining problematic characters that could break JSON
    sanitized = re.sub(r'[^\x20-\x7E\n\r\t]', '', sanitized)  # Keep only ASCII printable + whitespace

    # Clean up any double spaces or weird whitespace
    sanitized = re.sub(r'\s+', ' ', sanitized)  # Normalize whitespace
    sanitized = sanitized.replace('\n ', '\n').replace('\r ', '\r')  # Clean line breaks

    return sanitized


def extract_json_from_response(content: str) -> Optional[dict]:
    """Extracts JSON from markdown-formatted response with enhanced parsing for truncated responses."""
    try:
        # Clean the content first
        content = content.strip()

        # Handle empty or whitespace-only responses
        if not content:
            return None

        # Sanitize control characters that can break JSON parsing
        content = sanitize_json_string(content)

        # Handle empty content after sanitization
        if not content:
            return None

        # Check if response appears to be truncated
        is_truncated = (
            content.endswith("...") or
            content.endswith("ne...") or  # Common grok-beta truncation pattern
            len(content) > 100 and not content.endswith("}") and not content.endswith("]")
        )

        # Method 1: Try to find JSON in markdown code blocks
        json_patterns = ["```json", "```JSON", "```"]
        for pattern in json_patterns:
            json_start = content.find(pattern)
            if json_start != -1:
                json_text = content[json_start + len(pattern):]
                json_end = json_text.find("```")
                if json_end != -1:
                    json_text = json_text[:json_end].strip()
                else:
                    # Handle truncated code blocks
                    json_text = json_text.strip()

                # Skip empty JSON text
                if not json_text:
                    continue

                # Try to parse as-is first
                try:
                    result = json.loads(json_text)
                    return result
                except json.JSONDecodeError as e:
                    # Always try to repair the JSON for common formatting issues
                    repaired_json = attempt_json_repair(json_text)
                    if repaired_json:
                        return repaired_json
                    continue

        # Method 2: Try to parse the entire content as JSON
        try:
            result = json.loads(content)
            return result
        except json.JSONDecodeError as e:
            # If truncated, try to repair
            if is_truncated:
                repaired_json = attempt_json_repair(content)
                if repaired_json:
                    return repaired_json

        # Method 3: Look for { and } to extract JSON with better brace matching
        start_brace = content.find("{")
        if start_brace != -1:
            # Find the matching closing brace
            brace_count = 0
            end_brace = start_brace
            for i, char in enumerate(content[start_brace:], start_brace):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end_brace = i
                        break

            if brace_count == 0:  # Found matching braces
                json_text = content[start_brace:end_brace + 1]
                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    pass
            elif is_truncated:
                # Try to repair truncated JSON
                json_text = content[start_brace:]
                repaired_json = attempt_json_repair(json_text)
                if repaired_json:
                    return repaired_json

        # Method 4: Try to extract JSON-like content using regex
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, content, re.DOTALL)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # Method 5: Enhanced pattern matching for reflection analysis
        # Look for reflection-specific patterns
        decision_quality_match = re.search(r'"decision_quality"\s*:\s*"([^"]+)"', content, re.IGNORECASE)
        correctness_score_match = re.search(r'"correctness_score"\s*:\s*(\d+(?:\.\d+)?)', content, re.IGNORECASE)
        key_insights_match = re.search(r'"key_insights"\s*:\s*\[(.*?)\]', content, re.IGNORECASE | re.DOTALL)
        recommendations_match = re.search(r'"recommendations"\s*:\s*\[(.*?)\]', content, re.IGNORECASE | re.DOTALL)
        reasoning_match = re.search(r'"reasoning"\s*:\s*"([^"]*)"', content, re.IGNORECASE | re.DOTALL)

        if decision_quality_match and correctness_score_match:
            result = {
                "decision_quality": decision_quality_match.group(1),
                "correctness_score": float(correctness_score_match.group(1)),
                "key_insights": [],
                "recommendations": [],
                "reasoning": "Extracted from truncated response"
            }

            # Try to extract arrays
            if key_insights_match:
                try:
                    insights_text = key_insights_match.group(1)
                    # Simple parsing for string arrays
                    insights = [s.strip().strip('"') for s in insights_text.split(',') if s.strip()]
                    result["key_insights"] = insights
                except:
                    pass

            if recommendations_match:
                try:
                    recs_text = recommendations_match.group(1)
                    recs = [s.strip().strip('"') for s in recs_text.split(',') if s.strip()]
                    result["recommendations"] = recs
                except:
                    pass

            if reasoning_match:
                result["reasoning"] = reasoning_match.group(1)

            return result

        # Method 6: Fallback for basic signal patterns
        signal_match = re.search(r'"signal"\s*:\s*"([^"]+)"', content, re.IGNORECASE)
        confidence_match = re.search(r'"confidence"\s*:\s*(\d+(?:\.\d+)?)', content, re.IGNORECASE)
        reasoning_match = re.search(r'"reasoning"\s*:\s*"([^"]+)"', content, re.IGNORECASE)

        if signal_match and confidence_match and reasoning_match:
            return {
                "signal": signal_match.group(1),
                "confidence": float(confidence_match.group(1)),
                "reasoning": reasoning_match.group(1)
            }

    except Exception as e:
        print(f"Error extracting JSON from response: {e}")
    return None


def preprocess_malformed_json(json_text: str) -> str:
    """Preprocess JSON text to fix common glm-4-flash formatting issues."""
    import re

    # Fix specific glm-4-flash issues
    # 1. Fix "confidence 80, -> "confidence": 80,
    json_text = re.sub(r'"confidence\s+(\d+(?:\.\d+)?),', r'"confidence": \1,', json_text)

    # 2. Fix missing quotes around property names
    json_text = re.sub(r'([{,]\s*)([a-zA-Z_]\w*)\s*:', r'\1"\2":', json_text)

    # 3. Remove inline comments more carefully
    lines = json_text.split('\n')
    cleaned_lines = []
    for line in lines:
        # Simple approach: remove // comments that appear after values
        if '//' in line:
            # Find the position of // that's not inside quotes
            in_quotes = False
            comment_pos = -1
            for i in range(len(line) - 1):
                if line[i] == '"' and (i == 0 or line[i-1] != '\\'):
                    in_quotes = not in_quotes
                elif line[i:i+2] == '//' and not in_quotes:
                    comment_pos = i
                    break

            if comment_pos >= 0:
                line = line[:comment_pos].rstrip()
                # Add comma if needed for values that should have commas
                if (line.endswith(' 0') or line.endswith(' 60.0') or
                    line.endswith('"sell"') or line.endswith('"buy"')):
                    line += ','

        cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)


def attempt_json_repair(json_text: str) -> Optional[dict]:
    """Attempts to repair truncated or malformed JSON with enhanced grok-beta specific handling."""
    try:
        import re

        # Handle empty or whitespace-only input
        if not json_text or not json_text.strip():
            return None

        # Sanitize the input first
        json_text = sanitize_json_string(json_text.strip())

        # Handle completely empty responses after sanitization
        if not json_text:
            return None

        # Preprocess for glm-4-flash specific issues
        json_text = preprocess_malformed_json(json_text)

        # Try parsing as-is first (might already be valid)
        try:
            result = json.loads(json_text)
            return result
        except json.JSONDecodeError as e:
            # Continue with repair attempts
            pass

        # Try parsing after preprocessing
        try:
            result = json.loads(json_text)
            print(f"✅ Successfully repaired JSON with preprocessing")
            return result
        except json.JSONDecodeError as e:
            # Continue with more advanced repair attempts
            pass

        # Store original for fallback
        original_json = json_text

        # Special handling for grok-beta specific truncation patterns
        grok_truncation_patterns = [
            (r'with \d+ bullish, \d+ bearish, and \d+ ne\.\.\.', ''),  # Remove specific truncated text
            (r'ne\.\.\.', ''),  # Remove "ne..." pattern
            (r'\.\.\.', ''),   # Remove general "..." pattern
        ]

        original_json = json_text
        for pattern, replacement in grok_truncation_patterns:
            json_text = re.sub(pattern, replacement, json_text)

        # Extract JSON from markdown if present
        json_match = re.search(r'```(?:json)?\s*(\{.*?)(?:\s*```|$)', json_text, re.DOTALL)
        if json_match:
            json_text = json_match.group(1).strip()
        else:
            # Look for JSON object in the text - more flexible pattern
            json_match = re.search(r'\{.*', json_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0).strip()

        # Handle different types of truncation
        if not json_text.endswith("}") and not json_text.endswith("]"):
            # Strategy 1: Try to close incomplete arrays first
            if '"recommendations": [' in json_text and not ']' in json_text.split('"recommendations": [')[1]:
                # Find the position after "recommendations": [
                array_start = json_text.find('"recommendations": [') + len('"recommendations": [')
                before_array = json_text[:array_start]
                # Close the array and object
                json_text = before_array + ']}'

            # Strategy 2: Find the last complete field by looking for the last comma or opening brace
            else:
                last_comma = json_text.rfind(",")
                last_brace = json_text.rfind("{")

                if last_comma > last_brace:
                    # Truncate at the last comma and add closing brace
                    json_text = json_text[:last_comma] + "}"
                elif last_brace != -1:
                    # Find the last complete field before truncation
                    # Look for pattern like "field": "value" or "field": number
                    patterns_to_try = [
                        r'("[^"]+"\s*:\s*"[^"]*")\s*,?\s*[^,}]*$',  # String values
                        r'("[^"]+"\s*:\s*[\d.]+)\s*,?\s*[^,}]*$',   # Numeric values
                        r'("[^"]+"\s*:\s*\[[^\]]*\])\s*,?\s*[^,}]*$',  # Complete array values
                        r'("[^"]+"\s*:\s*\[[^\]]*)\s*[^,}]*$',  # Incomplete array values - close the array
                    ]

                    repaired = False
                    for i, pattern in enumerate(patterns_to_try):
                        match = re.search(pattern, json_text)
                        if match:
                            # Keep everything up to the last complete field
                            end_pos = match.start() + len(match.group(1))
                            if i == 3:  # Incomplete array case
                                json_text = json_text[:end_pos] + "]}"
                            else:
                                json_text = json_text[:end_pos] + "}"
                            repaired = True
                            break

                    if not repaired:
                        # Try to find any complete key-value pair
                        complete_pairs = re.findall(r'"[^"]+"\s*:\s*(?:"[^"]*"|[\d.]+|\[[^\]]*\])', json_text)
                        if complete_pairs:
                            # Rebuild JSON with complete fields only
                            json_text = "{" + ",".join(complete_pairs) + "}"
                        else:
                            # Just add closing brace
                            json_text = json_text.rstrip(",") + "}"
                else:
                    # No opening brace found, this might not be JSON at all
                    return None

        # Clean up common issues
        json_text = re.sub(r',\s*}', '}', json_text)  # Remove trailing commas
        json_text = re.sub(r',\s*]', ']', json_text)  # Remove trailing commas in arrays

        # Try to parse the repaired JSON
        try:
            result = json.loads(json_text)
            print(f"✅ Successfully repaired JSON from: {original_json[:100]}...")
            return result
        except json.JSONDecodeError as e:
            print(f"❌ JSON repair failed even after processing: {e}")
            return None

    except Exception as e:
        print(f"❌ JSON repair exception: {e}")
        return None


def is_response_truncated(content: str, parsed_result: dict) -> bool:
    """Detects if a response appears to be truncated with improved accuracy."""
    try:
        # 更严格的截断检测，减少误判
        strong_truncation_indicators = [
            content.endswith("..."),
            content.endswith("ne..."),  # Common grok-beta pattern
            "with 6 bullish, 8 bearish, and 4 ne" in content,  # Specific pattern from error logs
            content.endswith("...\""),  # JSON string truncated
            content.endswith("...}"),   # JSON object truncated
        ]

        # 强截断指标 - 确定是截断
        if any(strong_truncation_indicators):
            return True

        # 弱截断指标 - 需要结合其他条件判断
        weak_truncation_indicators = [
            len(content) > 100 and not content.rstrip().endswith("}") and not content.rstrip().endswith("]"),
            len(content) > 100 and content.rstrip().endswith(","),  # 以逗号结尾可能是截断
        ]

        # 只有在有强截断指标或者多个弱指标时才判断为截断
        weak_indicators_count = sum(weak_truncation_indicators)

        # 检查解析结果的完整性
        if isinstance(parsed_result, dict):
            # 检查必要字段是否存在且合理
            required_fields_missing = 0

            # 对于反思分析，检查关键字段
            if "reasoning" in parsed_result:
                reasoning = parsed_result["reasoning"]
                if not isinstance(reasoning, str) or len(reasoning.strip()) < 10:
                    required_fields_missing += 1

            # 检查信号字段
            if "signal" in parsed_result:
                signal = parsed_result.get("signal", "")
                if not signal or signal not in ["bullish", "bearish", "neutral", "buy", "sell", "hold"]:
                    required_fields_missing += 1

            # 检查置信度字段
            if "confidence" in parsed_result:
                confidence = parsed_result.get("confidence")
                if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                    required_fields_missing += 1

            # 如果有多个字段缺失或异常，可能是截断
            if required_fields_missing >= 2:
                weak_indicators_count += 1

        # 只有在有足够证据时才判断为截断
        return weak_indicators_count >= 2

    except Exception:
        return False


def optimize_prompt_for_grok(prompt: Any, attempt: int) -> Any:
    """
    Optimizes prompts for grok-beta model to prevent response truncation.
    Uses more effective strategies based on attempt number.

    Args:
        prompt: The original prompt (can be string, list of messages, or ChatPromptTemplate)
        attempt: The current attempt number (1-based)

    Returns:
        Optimized prompt with reduced content to prevent truncation
    """
    try:
        from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage

        # 更有效的优化策略
        if attempt == 2:
            # 第一次优化：明确要求简洁响应和JSON格式
            optimization_instruction = (
                "\n\nIMPORTANT CONSTRAINTS:\n"
                "- Response must be valid JSON format only\n"
                "- Keep reasoning field under 200 characters\n"
                "- Use concise, direct language\n"
                "- No explanatory text outside JSON\n"
                "- Focus on essential information only"
            )
        elif attempt == 3:
            # 第二次优化：更严格的约束
            optimization_instruction = (
                "\n\nCRITICAL REQUIREMENTS:\n"
                "- JSON response only, no additional text\n"
                "- Reasoning field: maximum 100 characters\n"
                "- Use bullet points or short phrases\n"
                "- Essential data only\n"
                "- No verbose explanations"
            )
        else:
            # 最后尝试：极简响应
            optimization_instruction = (
                "\n\nURGENT - MINIMAL RESPONSE:\n"
                "- Pure JSON only\n"
                "- Reasoning: max 50 chars\n"
                "- Key points only\n"
                "- No extra text"
            )

        # Handle different prompt types
        if isinstance(prompt, str):
            # Simple string prompt - also try to reduce original content
            if attempt >= 3:
                # For final attempts, try to shorten the original prompt too
                lines = prompt.split('\n')
                # Keep only essential lines (first few and last few)
                if len(lines) > 10:
                    essential_lines = lines[:5] + ['...'] + lines[-3:]
                    shortened_prompt = '\n'.join(essential_lines)
                    return shortened_prompt + optimization_instruction
            return prompt + optimization_instruction

        elif isinstance(prompt, list):
            # List of messages - optimize the last human message and potentially shorten system message
            optimized_messages = []
            for i, msg in enumerate(prompt):
                try:
                    if hasattr(msg, 'content') and hasattr(msg, '__class__'):
                        if 'Human' in msg.__class__.__name__:
                            # Optimize human message
                            if isinstance(msg.content, str):
                                optimized_content = msg.content + optimization_instruction
                                optimized_msg = type(msg)(content=optimized_content)
                                optimized_messages.append(optimized_msg)
                            else:
                                optimized_messages.append(msg)
                        elif 'System' in msg.__class__.__name__ and attempt >= 3:
                            # For final attempts, try to shorten system message
                            if isinstance(msg.content, str) and len(msg.content) > 500:
                                # Keep first and last parts of system message
                                content = msg.content
                                if len(content) > 1000:
                                    shortened_content = content[:400] + "\n...\n" + content[-200:]
                                    optimized_msg = type(msg)(content=shortened_content)
                                    optimized_messages.append(optimized_msg)
                                else:
                                    optimized_messages.append(msg)
                            else:
                                optimized_messages.append(msg)
                        else:
                            optimized_messages.append(msg)
                    else:
                        optimized_messages.append(msg)
                except Exception:
                    # If we can't process this message, keep it as-is
                    optimized_messages.append(msg)
            return optimized_messages

        else:
            # For other types (ChatPromptTemplate, etc.)
            try:
                if hasattr(prompt, 'format') and callable(prompt.format):
                    prompt_str = str(prompt)
                    return prompt_str + optimization_instruction
                else:
                    prompt_str = str(prompt)
                    return prompt_str + optimization_instruction
            except Exception:
                return prompt

    except Exception as e:
        print(f"Warning: Failed to optimize prompt for grok-beta: {e}")
        return prompt
