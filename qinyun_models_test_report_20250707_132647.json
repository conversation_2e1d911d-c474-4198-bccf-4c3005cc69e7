{"test_time": "2025-07-07T13:26:47.616865", "total_models": 16, "successful_count": 0, "failed_count": 16, "success_rate": 0.0, "successful_models": [], "failed_models": [{"model_name": "claude-3-5-sonnet-latest", "display_name": "[q<PERSON><PERSON>] claude-3.5-sonnet", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gemini-2.0-flash", "display_name": "[qingyun] gemini-2.0-flash", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gemini-2.5-pro-exp-03-25", "display_name": "[q<PERSON><PERSON>] gemini-2.5-pro", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-4.5-preview", "display_name": "[qingyun] gpt-4.5", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-4o", "display_name": "[qingyun] gpt-4o", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-3.5-turbo", "display_name": "[qingyun] gpt-3.5-turbo", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "o3", "display_name": "[qingyun] o3", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "o4-mini", "display_name": "[qingyun] o4-mini", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "meta-llama/llama-4-scout", "display_name": "[qingyun] meta-llama/llama-4-scout", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "meta-llama/llama-4-maverick", "display_name": "[qingyun] meta-llama/llama-4-maverick", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-beta", "display_name": "[qing<PERSON>] grok-beta", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-3-reasoner", "display_name": "[q<PERSON><PERSON>] grok-3-reasoner", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-2-1212", "display_name": "[qingyun] grok-2-1212", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "llama-3-sonar-large-32k-chat", "display_name": "[qingyun] llama-3-sonar-large-32k-chat", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "glm-4-flash", "display_name": "[qingyun] glm-4-flash", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "llama-2-70b", "display_name": "[q<PERSON><PERSON>] llama-2-70b", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}], "all_results": [{"model_name": "claude-3-5-sonnet-latest", "display_name": "[q<PERSON><PERSON>] claude-3.5-sonnet", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gemini-2.0-flash", "display_name": "[qingyun] gemini-2.0-flash", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gemini-2.5-pro-exp-03-25", "display_name": "[q<PERSON><PERSON>] gemini-2.5-pro", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-4.5-preview", "display_name": "[qingyun] gpt-4.5", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-4o", "display_name": "[qingyun] gpt-4o", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "gpt-3.5-turbo", "display_name": "[qingyun] gpt-3.5-turbo", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "o3", "display_name": "[qingyun] o3", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "o4-mini", "display_name": "[qingyun] o4-mini", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "meta-llama/llama-4-scout", "display_name": "[qingyun] meta-llama/llama-4-scout", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "meta-llama/llama-4-maverick", "display_name": "[qingyun] meta-llama/llama-4-maverick", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-beta", "display_name": "[qing<PERSON>] grok-beta", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-3-reasoner", "display_name": "[q<PERSON><PERSON>] grok-3-reasoner", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "grok-2-1212", "display_name": "[qingyun] grok-2-1212", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "llama-3-sonar-large-32k-chat", "display_name": "[qingyun] llama-3-sonar-large-32k-chat", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "glm-4-flash", "display_name": "[qingyun] glm-4-flash", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}, {"model_name": "llama-2-70b", "display_name": "[q<PERSON><PERSON>] llama-2-70b", "status": "failed", "error": "call_llm() got an unexpected keyword argument 'provider'"}]}