# AI对冲基金系统 - MSFT & AAPL 股票价格分析报告

## 📊 项目概述

本项目基于AI对冲基金回测系统的价格数据获取架构，为MSFT（微软）和AAPL（苹果）股票创建了详细的价格波动可视化图表。分析时间范围为2025年1月1日至2025年6月1日，参考了NVDA_price_chart_2025.png的样式模板。

## 🔧 系统架构分析

### 价格数据获取方式
- **主要数据源**: Financial Datasets API (https://api.financialdatasets.ai)
- **备用数据源**: 本地缓存系统
- **数据格式**: OHLCV (开盘价、最高价、最低价、收盘价、成交量)
- **时间间隔**: 日线数据

### 核心技术组件
- **get_prices()**: 获取价格数据，支持缓存机制
- **get_price_data()**: 返回pandas DataFrame格式数据
- **Price模型**: Pydantic数据模型，包含OHLCV字段
- **缓存机制**: 内存缓存避免重复API调用，按股票代码分别缓存

### API配置要求
- **环境变量**: FINANCIAL_DATASETS_API_KEY
- **请求头**: X-API-KEY
- **重试机制**: 内置错误处理和容错机制

## 📈 分析结果

### MSFT (微软) 股票表现
- **分析期间**: 2025-01-02 到 2025-05-30 (102个交易日)
- **起始价格**: $418.58
- **结束价格**: $460.36
- **总收益率**: +9.98%
- **价格区间**: $344.79 - $462.52
- **最大回撤**: -20.72%
- **年化波动率**: 32.27%
- **平均成交量**: 23.8M

### AAPL (苹果) 股票表现
- **分析期间**: 2025-01-02 到 2025-05-30 (102个交易日)
- **起始价格**: $243.85
- **结束价格**: $200.85
- **总收益率**: -17.63%
- **价格区间**: $169.21 - $250.00
- **最大回撤**: -30.22%
- **年化波动率**: 44.10%
- **平均成交量**: 58.6M

### 对比分析
- **表现差异**: MSFT表现明显优于AAPL，收益率相差约27.61%
- **波动性**: AAPL波动率(44.10%)显著高于MSFT(32.27%)
- **风险调整**: MSFT在风险调整后的表现更优
- **成交量**: AAPL平均成交量约为MSFT的2.5倍

## 📁 生成文件

### 图表文件
1. **MSFT_price_chart_2025.png** - 微软股票价格走势图
   - 包含收盘价走势线
   - 日内波动范围填充
   - 5日和20日移动平均线
   - 成交量柱状图

2. **AAPL_price_chart_2025.png** - 苹果股票价格走势图
   - 相同的技术指标和图表样式
   - 中文标签和说明
   - 高分辨率PNG格式

3. **MSFT_vs_AAPL_comparison_2025.png** - 对比分析图表
   - 标准化收益率对比
   - 双Y轴价格对比
   - 直观的性能差异展示

### 脚本文件
- **create_stock_price_charts.py** - 主要分析脚本
  - 完整的数据获取和处理逻辑
  - 统计分析功能
  - 图表生成和保存
  - 中文界面支持

## 🎨 图表特性

### 设计规范
- **样式一致性**: 参考NVDA_price_chart_2025.png模板
- **中文支持**: 完整的中文标签和说明
- **高质量输出**: 300 DPI分辨率
- **专业布局**: 双子图设计（价格+成交量）

### 技术指标
- **移动平均线**: 5日和20日MA
- **价格区间**: 日内高低点填充
- **成交量**: 独立子图显示
- **网格线**: 增强可读性

### 颜色方案
- **收盘价**: 蓝色主线 (#1f77b4)
- **波动范围**: 浅蓝色填充
- **5日均线**: 橙色 (orange)
- **20日均线**: 红色 (red)
- **成交量**: 灰色柱状图

## 🚀 使用方法

### 环境要求
```bash
pip install pandas matplotlib numpy
```

### 运行脚本
```bash
python create_stock_price_charts.py
```

### 环境变量配置
```bash
export FINANCIAL_DATASETS_API_KEY="your_api_key_here"
```

## 📊 技术实现

### 数据处理流程
1. **API调用**: 使用项目内置的get_prices()函数
2. **数据清洗**: 转换为pandas DataFrame格式
3. **技术指标**: 计算移动平均线和统计指标
4. **可视化**: matplotlib生成专业图表
5. **保存输出**: 高质量PNG格式

### 错误处理
- **API失败**: 自动重试机制
- **数据缺失**: 优雅降级处理
- **网络问题**: 详细错误提示
- **配置检查**: 环境变量验证

## 📝 结论

本次分析成功展示了AI对冲基金系统的价格数据处理能力，生成了专业级别的股票价格分析图表。MSFT在分析期间表现优于AAPL，但AAPL显示出更高的波动性和交易活跃度。所有图表均采用统一的专业样式，支持中文显示，可直接用于投资分析和报告。

---
*生成时间: 2025年7月7日*  
*数据来源: Financial Datasets API*  
*技术栈: Python, pandas, matplotlib, AI对冲基金回测系统*
